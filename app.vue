<template>
  <NuxtLayout>
    <NuxtPage />
  </NuxtLayout>
</template>

<script setup lang="ts">
// Global SEO setup
const {locale} = useI18n()

// Set up global SEO with structured data
useSeo({
  structuredData: createOrganizationSchema(),
})

// Set up title template for all pages
useHead({
  titleTemplate: (titleChunk) => {
    return titleChunk 
      ? `${titleChunk} | Glacier Paradise` 
      : 'Glacier Paradise - Special Sightseeing Tours of Snæfellsjökull'
  },
  htmlAttrs: {
    lang: () => locale.value || 'en',
  },
})

// Add global structured data for LocalBusiness
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify(createLocalBusinessSchema()),
    },
  ],
})

// Set up language-specific meta tags
watch(() => locale.value, (newLocale) => {
  if (newLocale) {
    useHead({
      htmlAttrs: {
        lang: newLocale,
      },
      meta: [
        {
          property: 'og:locale',
          content: newLocale === 'en' ? 'en_US' : 'is_IS',
        },
      ],
    })
  }
}, { immediate: true })
</script>
