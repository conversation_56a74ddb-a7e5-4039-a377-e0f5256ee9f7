@use "./variables.scss" as *;
@use "@/assets/mixins.scss" as *;

//BE Vietnam Pro
// @import url("https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

@import url("https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;600&display=swap");

body {
  font-family: sans-serif;
  font-family: "Be Vietnam Pro", sans-serif;
}

html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
  padding: 0;
}

p {
  font-weight: 300;
  font-size: 18px;
}

.bm-overlay {
  .bm-burger-button {
    width: 42px;
    top: 46px;
  }

  .bm-burger-bars {
    background-color: $yellow;
  }
}

.bm-menu {
  @include ellipse-bg-midnight_blue-gradient(bottom);
}

.bm-item-list {
  a {
    color: $yellow;

    &:hover {
      color: $white;
    }
  }
}

// .page {
//   // padding-top: $header_height;
//   // min-height: calc(100vh - $header_height - $footer_height);
// }

.overflow-hidden {
  overflow: hidden;
}

button.primary {
  margin-top: 1rem;
  display: inline-block;
  background: #d4af37;
  border-radius: 5px;
  box-shadow: none;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  text-align: center;
  color: #43464e;
  border: none;
  cursor: pointer;
  transition: background 0.2s ease;
  margin: auto;
  height: 2.5rem;
  width: 10rem;
  box-sizing: border-box;

  &.small {
    font-weight: 400;
    font-size: 12px;
    height: 1.5rem;
    width: 8rem;
  }
}

button.primary:hover {
  background: #9d8022;
}

button.primary:active,
button.primary:focus {
  background: #b29126;
}
