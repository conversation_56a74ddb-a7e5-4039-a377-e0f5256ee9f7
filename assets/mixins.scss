@use "./variables.scss" as *;

@mixin for-phone-only {
  @media (max-width: 599px) {
    @content;
  }
}

@mixin for-tablet-portrait-down {
  @media (max-width: 600px) {
    @content;
  }
}

@mixin for-tablet-portrait-up {
  @media (min-width: 600px) {
    @content;
  }
}

@mixin for-tablet-landscape-down {
  @media (max-width: 900px) {
    @content;
  }
}

@mixin for-tablet-landscape-up {
  @media (min-width: 900px) {
    @content;
  }
}

@mixin for-desktop-up {
  @media (min-width: 1200px) {
    @content;
  }
}

@mixin for-medium-desktop-up {
  @media (min-width: 1500px) {
    @content;
  }
}

@mixin for-big-desktop-up {
  @media (min-width: 1800px) {
    @content;
  }
}

@mixin yellow-brownish-bg-gradient($deg: 135deg) {
  background: linear-gradient($deg, #d4af37, #4d4637);

  a {
    color: $blue;
  }
}

@mixin yellow-brownish-with-animation-bg-gradient {
  background: radial-gradient(circle at bottom, #d4af37, #4d4637);
  animation: gradient 1s ease-in-out infinite;
  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
}

@mixin gold-bg-gradient($deg: 165deg) {
  background-image: linear-gradient(
    $deg,
    #462523 0,
    #cb9b51,
    #f6e27a,
    #f6f2c0,
    #462523
  );
  box-shadow: inset 0 0 7px #cb9b51, 0 0 7px #cb9b51;
}

@mixin ellipse-bg-dark_gray-gradient {
  background: radial-gradient(ellipse at right, #d4af37 3%, $dark_grey 49%);
  animation: gradient-43347d14 6s ease infinite;
}

@mixin ellipse-bg-midnight_blue-gradient($direction: right) {
  // background: radial-gradient(ellipse at right, #d4af37 3%, #191970 49%);
  background: radial-gradient(ellipse at $direction, #d4af37 3%, #071a5a 49%);

  animation: gradient-43347d14 6s ease infinite;
}

@mixin visions_of_grandeur {
  background: #000046;
  background: -webkit-linear-gradient(to right, $blue, #000046);
  background: linear-gradient(to right, $blue, #000046);
}

@mixin white-blue-bg-gradient($deg: 0deg) {
  background: linear-gradient(
    $deg,
    #071a5a,
    #192f6a,
    #5e738e,
    #b6b2b8,
    #e8e5e0
  );
}
