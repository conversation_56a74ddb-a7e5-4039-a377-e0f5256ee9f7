// Define interface locally to avoid import issues
interface ICardInfo {
  imageSrc: string;
  imageAlt: string;
  cardHeader: string;
  cardText: string;
  cardAdditionalText: string;
  linkText: string;
  linkHref: string;
  isNew?: boolean;
}

export const cardsInfo: ICardInfo[] = [
  {
    imageSrc: "topoftthediamond-resized.card_fnwonh",
    imageAlt: "home.tours.topOfTheDiamond.imageAlt",
    cardHeader: "home.tours.topOfTheDiamond.header",
    cardText: "home.tours.topOfTheDiamond.text",
    cardAdditionalText: "home.tours.topOfTheDiamond.additionalText",
    linkText: "shared.links.readMore",
    linkHref: "/tours/snow-cat",
    isNew: false,
  },
  {
    imageSrc: "midnightsun-resized.card_ymp4gi",
    imageAlt: "home.tours.midnightSunTour.imageAlt",
    cardHeader: "home.tours.midnightSunTour.header",
    cardText: "home.tours.midnightSunTour.text",
    cardAdditionalText: "home.tours.midnightSunTour.additionalText",
    linkText: "shared.links.readMore",
    linkHref: "/tours/midnight-sun",
    isNew: false,
  },
  {
    imageSrc: "buggy-card-image-1_daqyhh",
    imageAlt: "home.tours.buggy.imageAlt",
    cardHeader: "home.tours.buggy.header",
    cardText: "home.tours.buggy.text",
    cardAdditionalText: "home.tours.buggy.additionalText",
    linkText: "shared.links.readMore",
    linkHref: "/tours/buggy",
    isNew: true,
  },
  {
    imageSrc: "custom_card_fn02v8",
    imageAlt: "home.tours.customTours.imageAlt",
    cardHeader: "home.tours.customTours.header",
    cardText: "home.tours.customTours.text",
    cardAdditionalText: "home.tours.customTours.additionalText",
    linkText: "shared.links.contactUs",
    linkHref: "/contactUs?subject=customTours",
    isNew: false,
  },
  {
    imageSrc: "cabin_northern_lights-resized_qs1nc6",
    imageAlt: "home.tours.cabin.imageAlt",
    cardHeader: "home.tours.cabin.header",
    cardText: "home.tours.cabin.text",
    cardAdditionalText: "home.tours.cabin.additionalText",
    linkText: "",
    linkHref: "",
    isNew: false,
  },

];
