# Glacier Paradise - Snæfellsjökull Tours Website

This is the official website for Glacier Paradise, offering special sightseeing tours of Snæfellsjökull glacier in Iceland. The website is built using Nuxt 3, a modern Vue.js framework.

## Project Overview

Glacier Paradise is a local family-owned business founded in 2022, offering unique tour experiences on the Snæfellsjökull glacier. This website serves as the main platform for:

- Showcasing available tours
- Providing information about the company and the glacier
- Enabling customers to book tours
- Offering contact options for inquiries

## Features

- **Multi-language Support**: English and Icelandic
- **Responsive Design**: Optimized for all device sizes
- **Tour Booking Integration**: Direct booking through Bokun widget
- **Weather Information**: Real-time weather data for the glacier area
- **Contact Form**: Integrated email functionality for inquiries
- **SEO Optimized**: Structured metadata and sitemap

## Technology Stack

- **Framework**: Nuxt 3
- **Language**: TypeScript
- **Styling**: SCSS
- **Icons**: oh-vue-icons
- **Internationalization**: @nuxtjs/i18n
- **Device Detection**: @nuxtjs/device
- **SEO**: nuxt-site-config, @nuxtjs/sitemap, @nuxtjs/robots
- **Email**: nuxt-mail

## Node.js Version

This project requires Node.js 18.x. An `.nvmrc` file is included for automatic version switching with nvm.

```bash
# If you have nvm installed, simply run:
nvm use
```

## Setup

Make sure to install the dependencies:

```bash
# npm
npm install
```

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```
NUXT_EMAIL_TO=<EMAIL>
NUXT_EMAIL_HOST=your-smtp-host
NUXT_EMAIL_PORT=your-smtp-port
NUXT_EMAIL_USERNAME=your-smtp-username
NUXT_EMAIL_PASSWORD=your-smtp-password
```

## Development Server

Start the development server on `http://localhost:3000`

```bash
npm run dev
```

## Production

Build the application for production:

```bash
npm run build
```

Locally preview production build:

```bash
npm run preview
```

Start the production server:

```bash
npm run start
```

## Linting

Run linting checks:

```bash
npm run lint
```

Fix linting issues:

```bash
npm run lintfix
```

## Documentation

Additional documentation can be found in the `docs` directory:

- [Remaining Issues](./docs/remaining-issues.md)
- [Improvement Suggestions](./docs/improvement-suggestions.md)
