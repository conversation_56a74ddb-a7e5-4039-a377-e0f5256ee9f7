/**
 * Cloudinary utility functions and presets
 */

/**
 * Predefined transformation presets for Cloudinary
 */
export const CloudinaryPresets = {
  RESPONSIVE_BACKGROUND: {
    width: 'auto',
    quality: 'auto',
    format: 'auto',
    dpr: 'auto',
    responsive: true,
  },
  CARD_THUMBNAIL: {
    width: 600,
    height: 400,
    crop: 'fill',
    gravity: 'auto',
    quality: 'auto',
    format: 'auto',
  },
  LOGO: {
    quality: 'auto',
    format: 'auto',
  },
  TEAM_PHOTO: {
    width: 300,
    height: 300,
    crop: 'fill',
    gravity: 'face',
    quality: 'auto',
    format: 'auto',
  },
};

/**
 * Get a Cloudinary URL for an image
 * @param path The path to the image in Cloudinary (without the cloud name)
 * @param options Cloudinary transformation options
 * @returns The Cloudinary URL
 */
export const getCloudinaryUrl = (path: string, options: Record<string, any> = {}): string => {
  // Get the cloud name from the runtime config
  const runtimeConfig = useRuntimeConfig();
  const cloudName = runtimeConfig.public.cloudinary?.cloudName || '';

  if (!cloudName) {
    console.warn('Cloudinary cloud name not found in runtime config');
    return '';
  }

  // Build the transformation string
  const transformations: string[] = [];
  
  if (options.width) transformations.push(`w_${options.width}`);
  if (options.height) transformations.push(`h_${options.height}`);
  if (options.crop) transformations.push(`c_${options.crop}`);
  if (options.gravity) transformations.push(`g_${options.gravity}`);
  if (options.quality) transformations.push(`q_${options.quality}`);
  if (options.format) transformations.push(`f_${options.format}`);
  if (options.dpr) transformations.push(`dpr_${options.dpr}`);
  
  const transformationString = transformations.length > 0 ? transformations.join(',') + '/' : '';
  
  // Build the URL
  return `https://res.cloudinary.com/${cloudName}/image/upload/${transformationString}${path}`;
};
