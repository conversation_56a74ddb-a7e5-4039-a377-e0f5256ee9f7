export interface SeoOptions {
  title?: string
  description?: string
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product' | 'profile'
  locale?: string
  siteName?: string
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player'
  structuredData?: Record<string, any>
}

export const useSeo = (options: SeoOptions = {}) => {
  const { $i18n } = useNuxtApp()
  const route = useRoute()
  const runtimeConfig = useRuntimeConfig()

  // Default values
  const defaults = {
    siteName: runtimeConfig.public.siteName || 'Glacier Paradise',
    siteUrl: runtimeConfig.public.siteUrl || 'https://www.glacierparadise.is',
    description: runtimeConfig.public.siteDescription || 'Discover the natural wonders of Snæfellsjökull with our exciting tours.',
    image: `${runtimeConfig.public.siteUrl}/images/og-default.jpg`,
    locale: $i18n?.locale?.value || 'en',
    type: 'website' as const,
    twitterCard: 'summary_large_image' as const,
  }

  // Merge options with defaults
  const seoData = {
    ...defaults,
    ...options,
  }

  // Generate canonical URL
  const canonicalUrl = options.url || `${defaults.siteUrl}${route.path}`

  // Set up SEO meta tags
  useSeoMeta({
    title: seoData.title,
    description: seoData.description,
    
    // Open Graph
    ogTitle: seoData.title,
    ogDescription: seoData.description,
    ogImage: seoData.image,
    ogUrl: canonicalUrl,
    ogType: seoData.type,
    ogSiteName: seoData.siteName,
    ogLocale: seoData.locale === 'en' ? 'en_US' : 'is_IS',
    
    // Twitter Card
    twitterCard: seoData.twitterCard,
    twitterTitle: seoData.title,
    twitterDescription: seoData.description,
    twitterImage: seoData.image,
    twitterSite: '@glacierparadise',
    
    // Additional meta tags
    robots: 'index, follow',
    author: 'Glacier Paradise',
  })

  // Set canonical URL
  useHead({
    link: [
      {
        rel: 'canonical',
        href: canonicalUrl,
      },
    ],
  })

  // Add hreflang tags for internationalization
  if ($i18n) {
    const hreflangLinks = $i18n.locales.value.map((locale: any) => ({
      rel: 'alternate',
      hreflang: locale.code === 'en' ? 'en-US' : 'is-IS',
      href: `${defaults.siteUrl}${locale.code === 'en' ? '' : `/${locale.code}`}${route.path}`,
    }))

    useHead({
      link: hreflangLinks,
    })
  }

  // Add structured data if provided
  if (options.structuredData) {
    useHead({
      script: [
        {
          type: 'application/ld+json',
          innerHTML: JSON.stringify(options.structuredData),
        },
      ],
    })
  }

  return {
    seoData,
    canonicalUrl,
  }
}

// Predefined structured data schemas
export const createOrganizationSchema = () => ({
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Glacier Paradise',
  url: 'https://www.glacierparadise.is',
  logo: 'https://www.glacierparadise.is/images/logo.png',
  description: 'Special sightseeing tours of Snæfellsjökull glacier in Iceland',
  address: {
    '@type': 'PostalAddress',
    addressCountry: 'IS',
    addressRegion: 'Snæfellsnes Peninsula',
  },
  contactPoint: {
    '@type': 'ContactPoint',
    contactType: 'customer service',
    email: '<EMAIL>',
  },
  sameAs: [
    'https://www.facebook.com/glacierparadise',
    'https://www.instagram.com/glacierparadise',
  ],
})

export const createLocalBusinessSchema = () => ({
  '@context': 'https://schema.org',
  '@type': 'LocalBusiness',
  '@id': 'https://www.glacierparadise.is/#organization',
  name: 'Glacier Paradise',
  url: 'https://www.glacierparadise.is',
  telephone: '+354-XXX-XXXX', // Add actual phone number
  email: '<EMAIL>',
  description: 'Special sightseeing tours of Snæfellsjökull glacier in Iceland',
  address: {
    '@type': 'PostalAddress',
    addressCountry: 'IS',
    addressRegion: 'Snæfellsnes Peninsula',
  },
  geo: {
    '@type': 'GeoCoordinates',
    latitude: 64.8088,
    longitude: -23.7750,
  },
  openingHours: 'Mo-Su 08:00-20:00',
  priceRange: '$$',
  servesCuisine: 'Tour Services',
})

export const createTourSchema = (tourData: {
  name: string
  description: string
  price?: string
  duration?: string
  location?: string
  image?: string
  url?: string
}) => ({
  '@context': 'https://schema.org',
  '@type': 'TouristTrip',
  name: tourData.name,
  description: tourData.description,
  image: tourData.image,
  url: tourData.url,
  provider: {
    '@type': 'Organization',
    name: 'Glacier Paradise',
    url: 'https://www.glacierparadise.is',
  },
  touristType: 'Adventure Seekers',
  itinerary: {
    '@type': 'ItemList',
    itemListElement: [
      {
        '@type': 'TouristDestination',
        name: tourData.location || 'Snæfellsjökull Glacier',
        description: 'Iconic glacier and national park in Iceland',
      },
    ],
  },
  ...(tourData.price && {
    offers: {
      '@type': 'Offer',
      price: tourData.price,
      priceCurrency: 'ISK',
      availability: 'https://schema.org/InStock',
    },
  }),
})
