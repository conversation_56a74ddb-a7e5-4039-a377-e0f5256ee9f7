import type { IWeat<PERSON> } from "@/interfaces/weather";

export const fetchWeather = async () => {
  const stapiLocations = {
    lat: "64.76936",
    lon: "-23.62258",
  };
  const getSymbolCode = (data: Record<string, any>): string => {
    if (data.next_1_hours) {
      return data.next_1_hours.summary.symbol_code;
    }
    if (data.next_6_hours) {
      return data.next_6_hours.summary.symbol_code;
    }
    if (data.next_12_hours) {
      return data.next_12_hours.summary.symbol_code;
    }
    return "unknown";
  };

  const weather: IWeather = await fetch(
    `https://api.met.no/weatherapi/locationforecast/2.0/compact?lat=${stapiLocations.lat}&lon=${stapiLocations.lon}`
  )
    .then((res) => res.json())
    .then((res) => {
      const data = res.properties.timeseries[0].data;
      const details = data.instant.details;
      const symbolCode = getSymbolCode(data);
      return {
        air_temperature: details.air_temperature,
        wind_speed: details.wind_speed,
        wind_from_direction: Math.round(details.wind_from_direction),
        symbol_code: symbolCode,
      };
    })
    .catch((_err) => {
      // Silent error handling - log to server in production
      return {
        air_temperature: 0,
        wind_speed: 0,
        wind_from_direction: 0,
        symbol_code: "unknown",
      };
    });

  return weather;
};
