import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import { onIntersect } from "./onIntersect";
import type { LocaleObject } from "@nuxtjs/i18n";

export const setupHeader = () => {
  const route = useRoute();

  const scrollRef = ref<HTMLElement>();

  const observer = ref({});
  let shouldCenterLogo = true;

  const onEnter = () => {
    if (route.path === "/") {
      document.getElementById("header")!.classList.remove("header-background");
      if (shouldCenterLogo) {
        const headerLogo = document.getElementById("header-logo")!;
        headerLogo.classList.add("center-logo");
      }
    }
  };

  const onExit = () => {
    if (route.path === "/") {
      document.getElementById("header")!.classList.add("header-background");
      if (shouldCenterLogo) {
        const headerLogo = document.getElementById("header-logo")!;
        const headerLogoPlaceholder = document.getElementById(
          "header-logo-placeholder"
        )!;
        headerLogo.classList.remove("center-logo");
        headerLogoPlaceholder.style.display = "none";
        shouldCenterLogo = false;
      }
    }
  };

  onMounted(() => {
    // Initialize the logo position on the landing page
    if (route.path === "/") {
      const headerLogo = document.getElementById("header-logo");
      if (headerLogo) {
        headerLogo.classList.add("center-logo");
      }
      const header = document.getElementById("header");
      if (header) {
        header.classList.remove("header-background");
      }
    } else {
      const header = document.getElementById("header");
      if (header) {
        header.classList.add("header-background");
      }
    }

    observer.value = onIntersect(
      scrollRef.value as HTMLElement,
      onEnter,
      onExit,
      false
    );
  });

  const showCenterLogo = computed(() => {
    console.log("showCenterLogo", route.path === "/" && shouldCenterLogo);

    return route.path === "/" && shouldCenterLogo;
  });

  return {
    scrollRef,
    showCenterLogo,
  };
};

export const setupRoutes = () => {
  const { setLocale } = useI18n();

  const routes = ref([
    {
      linkTo: "/",
      nameTranslationKey: "header.navLinks.home",
    },
    {
      linkTo: "/tours",
      nameTranslationKey: "header.navLinks.tours",
    },
    {
      linkTo: "/about",
      nameTranslationKey: "header.navLinks.about",
    },
    {
      linkTo: "/contactUs",
      nameTranslationKey: "header.navLinks.contact",
    },
    {
      linkTo: "/terms",
      nameTranslationKey: "header.navLinks.terms",
    },
  ]);

  const changeLocale = async (newLocale: LocaleObject) => {
    await setLocale(newLocale.code);
    (window as any).beam(`/custom-events/locale-swapped-${newLocale.name}`);
  };

  return {
    routes,
    changeLocale,
  };
};
