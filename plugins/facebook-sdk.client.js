// plugins/facebook-sdk.client.js
export default defineNuxtPlugin((nuxtApp) => {
  // Only run on client side
  if (process.client) {
    // const config = useRuntimeConfig();

    console.log("Facebook SDK loaded", nuxtApp.$config.public.facebookAppIdn);
    // Initialize Facebook SDK
    window.fbAsyncInit = function () {
      if (window.FB) {
        window.FB.init({
          appI: "326773569961916",
          cookie: true,
          xfbml: true,
          version: "v18.0",
        });
      }
    };

    // Load Facebook SDK asynchronously
    (function (d, s, id) {
      var js,
        fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s);
      js.id = id;
      js.src = "https://connect.facebook.net/en_US/sdk/xfbml.customerchat.js";
      fjs.parentNode.insertBefore(js, fjs);
    })(document, "script", "facebook-jssdk");
  }
});
