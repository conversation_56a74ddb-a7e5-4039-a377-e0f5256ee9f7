import { OhVueIcon, addIcons } from "oh-vue-icons";
import {
  FaBus,
  FaRegularAddressCard,
  FaRegularClock,
  FcPrevious,
  FcPhoneAndroid,
  FcNext,
  FaHamburger,
  FaBars,
  IoTimeOutline,
  IoClose,
  WiCelsius,
  WiDirectionUp,
  OiLocation,
} from "oh-vue-icons/icons";

// Add all icons to the library
addIcons(
  FaBus,
  FaRegularAddressCard,
  FaRegularClock,
  FcPhoneAndroid,
  FaHamburger,
  FaBars,
  IoTimeOutline,
  FcPrevious,
  FcNext,
  IoClose,
  WiCelsius,
  WiDirectionUp,
  OiLocation
);

export default defineNuxtPlugin((nuxtApp) => {
  // Register the component with kebab-case name
  nuxtApp.vueApp.component("v-icon", OhVueIcon);
});
