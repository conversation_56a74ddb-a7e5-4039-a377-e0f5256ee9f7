<template>
  <header ref="scrollRef">
    <div id="header" class="header">
      <div v-if="$route.path === '/'" id="header-logo-placeholder" class="header-logo-placeholder"></div>
      <div id="header-logo" class="header-logo">
        <CldImage id="header-image" src="logo-glacier-paradise-transparent_lyah7h" width="300" height="150"
          alt="Glacier paradise logo" loading="eager" />
      </div>

      <nav>
        <!-- TODO: is tours ref in use???? -->
        <div ref="toursRef" class="nav-routes">
          <template v-for="(route, index) in routes" :key="index">
            <div class="item">
              <nuxt-link :to="route.linkTo">{{
                $t(route.nameTranslationKey).toLocaleUpperCase()
              }}</nuxt-link>
            </div>
          </template>
        </div>
      </nav>

      <div class="locale-changer">
        <template v-for="lang in availableLocales">
          <button v-if="lang.code !== locale" :key="`locale-${lang}`" class="locale-button" @click="changeLocale(lang)">
            {{ lang.name }}
          </button>
        </template>
      </div>
    </div>
  </header>
</template>
<script lang="ts" setup>
import type { LocaleObject } from "@nuxtjs/i18n";
import { setupHeader } from "@/composables/headerShared";
const { locale, locales } = useI18n();

const availableLocales = computed(() => {
  return (locales.value as LocaleObject[]).filter(
    (i) => i.code !== locale.value
  );
});

const { scrollRef, showCenterLogo } = setupHeader();
const { routes, changeLocale } = setupRoutes();
</script>
<style lang="scss">
@use "@/assets/base.scss" as *;
@use "@/assets/variables.scss" as *;
@use "@/assets/mixins.scss" as *;

.header-background {
  background-color: $blue;

  // Only include the gradient mixin for tablet-landscape-up and larger screens
  @include for-tablet-landscape-up {
    @include ellipse-bg-midnight_blue-gradient;
  }

  // TODO: add transition to when it appears
}

.nav-routes {
  display: flex;
  justify-content: center;

  //TODO: duplicated in mobile-header, refactor to external file to maintain same look
  .item {
    padding-right: 1rem;

    button.nav-button {
      background: none;
      color: inherit;
      border: none;
      padding: 0;
      font: inherit;
      cursor: pointer;
      outline: inherit;
    }

    a,
    button.nav-button {
      color: $yellow;
      font-weight: 600;
      font-size: 20px;

      &.bottom-border {
        border-bottom: 1px solid;
      }
    }

    a:hover {
      color: $white;
    }

    a.router-link-active {
      color: $white;
    }
  }
}

.header {
  display: grid;
  grid-template-columns: 1fr 6fr 1fr;
  position: fixed;
  width: 100%;
  height: $header_height;
  align-items: center;
  z-index: 100;
  // box-sizing: border-box;

  // background-color: transparent;
  // transition: background-color 0.5s ease;

  nav {
    justify-self: center;
    grid-column: 2;
  }
}

.hide-image {
  display: none;
}

.header-logo,
.header-logo-placeholder {
  width: 6rem;
  justify-self: center;
  grid-column: 1;
  height: 3rem;
}

.header-logo {
  justify-self: center;
  transition: all 2s ease;
  grid-column: 1;

  :deep(img) {
    width: 100%;
    transition: 2s;
  }
}

.center-logo {
  position: fixed;
  top: 25vh;
  left: 50%;
  transform: translateX(-50%);
  width: 14rem;
  z-index: 101;
  transition: all 2s ease;

  :deep(img) {
    width: 100%;
    transition: 2s;
  }
}

.locale-changer {
  justify-self: center;
  grid-column: 3;
}

.locale-button {
  background: none;
  color: $white;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: large;
}

@include for-desktop-up {
  .header-logo {
    width: 8rem;
  }

  .item {
    padding-right: 2rem;

    a,
    button.nav-button {
      font-size: 24px;
    }
  }
}
</style>
