<template>
  <div id="footer__container" class="footer__container">
    <div class="footer-top-row">
      <div class="company-info">
        <h2>Glacier Paradise</h2>
        <div class="location">
          <p>Arnarstapavegur 2, 356 Snæfellsbær</p>
          <a href="https://maps.app.goo.gl/zdjQqRSjwrYbyD7P8" class="flip">
            <icon-wrapper name="oi-location" scale="1.2" color="#d4af37" />
          </a>
        </div>
        <p>+354 8612844</p>
        <p>+354 8657402</p>
      </div>
      <div class="iceland-map">
        <p class="reykjavik">Reykjavik</p>
        <p class="arnarstapi">Arnarstapi</p>
        <CldImage src="Map_Of_Iceland_clip_art_medium_uyuvml" width="150" height="100%" crop="scale"
          alt="Map of Iceland" loading="lazy" />
      </div>
    </div>
    <div class="footer-bottom-row">
      <div class="logos">
        <div class="social-media-row">
          <a class="logo" href="https://www.instagram.com/glacier.paradise/">
            <CldImage src="instagram-logo_h3kuhd" :width="imgSize" :height="imgSize" alt="Instagram" loading="lazy" />
          </a>
          <a class="logo" href="https://www.facebook.com/glacierparadise">
            <CldImage src="facebook-logo_fh8dtk" :width="imgSize" :height="imgSize" alt="Facebook" loading="lazy" />
          </a>
        </div>
      </div>
    </div>
    <!-- This will only be visible on tablet-landscape-up -->
    <div class="right-side">
      <div class="logos">
        <div class="social-media-row">
          <a class="logo" href="https://www.instagram.com/glacier.paradise/">
            <CldImage src="instagram-logo_h3kuhd" width="35" height="35" alt="Instagram" loading="lazy" />
          </a>
          <a class="logo" href="https://www.facebook.com/glacierparadise">
            <CldImage src="facebook-logo_fh8dtk" width="35" height="35" alt="Facebook" loading="lazy" />
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
const imgSize = 30; // Reduced by ~15% from 35
</script>
<style lang="scss" scoped>
@use "@/assets/base.scss" as *;
@use "@/assets/variables.scss" as *;
@use "@/assets/mixins.scss" as *;

.footer__container {
  background-color: $sapphire_blue;
  box-sizing: border-box;
  padding: 0.5rem 0;

  // For tablet landscape down - stacked layout
  display: flex;
  flex-direction: column;

  &.hide {
    display: none;
  }

  // For tablet landscape up - original layout
  @include for-tablet-landscape-up {
    height: $footer_height;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    align-items: center;
    justify-content: space-between;

    .footer-top-row {
      display: contents; // This allows children to be placed directly in the grid
    }

    .company-info {
      grid-column: 1;
    }

    .iceland-map {
      grid-column: 2;
      justify-self: center;
    }

    .footer-bottom-row {
      display: none;
    }
  }
}

.footer-top-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 1rem 0;
}

.footer-bottom-row {
  display: flex;
  justify-content: center; // Center the logos
  width: 100%;
  padding-bottom: 1rem;
}

.company-info {
  padding-left: 1rem;

  .location {
    display: flex;
    align-items: flex-end;
  }

  h2 {
    color: $yellow;
    font-size: 0.9rem;
  }

  p {
    color: $white;
    font-size: 0.7rem;
  }
}

.iceland-map {
  position: relative;
  justify-self: center;
  font-size: 0.6rem;
  width: 150px;
  height: 75px;
  display: flex;
  justify-content: center;
  align-items: center;

  .reykjavik {
    position: absolute;
    top: 49px;
    left: 37px;
    color: #D60036;
  }

  .arnarstapi {
    position: absolute;
    top: 29px;
    left: 17px;
    color: #D60036;
  }

  :deep(img) {
    width: 100%;
    height: auto;
    max-width: 150px;
  }

  @include for-tablet-landscape-up {
    width: 200px;
    height: 100px;

    .reykjavik {
      position: absolute;
      top: 63px;
      left: 60px;
      /* Color is inherited from parent rule */
    }

    .arnarstapi {
      position: absolute;
      top: 40px;
      left: 40px;
      /* Color is inherited from parent rule */
    }

    :deep(img) {
      max-width: 200px;
    }
  }
}

.right-side {
  display: none; // Hide in mobile layout

  @include for-tablet-landscape-up {
    display: grid;
    grid-column: 3;
    grid-template-columns: 1fr;
    min-height: 100%;
    box-sizing: border-box;
    padding: 1rem 1rem 1rem 0;

    .certificates {
      justify-self: end;

      img {
        width: 28px;
        height: auto;
      }
    }

    .logos {
      display: grid;
      grid-template-rows: auto;
      gap: 0.5rem;
      justify-self: end;
      align-items: end;

      .social-media-row {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
      }

      .logo {
        padding-right: 2rem;

        &:last-of-type {
          padding-right: unset;
        }

        :deep(img) {
          width: auto;
          height: 35px; // Consistent with mobile size
        }
      }
    }
  }
}

// Logos in the footer-bottom-row (mobile/tablet layout)
.footer-bottom-row .logos {

  .social-media-row {
    display: flex;
    justify-content: center;
  }

  .logo {
    padding: 0 0.5rem;

    :deep(img) {
      width: auto;
      height: 30px; // Reduced by ~15% from 35px
    }
  }
}
</style>
