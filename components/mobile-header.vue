<template>
  <header ref="scrollRef">
    <div id="header" class="header" :class="{ 'header-background': !showCenterLogo }">
      <div id="header-logo" class="header-logo" :class="{ 'center-logo': showCenterLogo }">
        <CldImage id="header-image" src="logo-glacier-paradise-transparent_lyah7h" width="300" height="150"
          alt="Glacier paradise logo" loading="eager" />
      </div>
      <div class="burger-button">
        <icon-wrapper name="fa-bars" scale="2.5" color="#dadada" @click="openDrawer" />
      </div>
    </div>
    <SlideNavigator :open-drawer="drawerIsOpen" @close="closeDrawer">
    </SlideNavigator>
  </header>
</template>

<script lang="ts" setup>
import { setupHeader } from "@/composables/headerShared";
const drawerIsOpen = ref(false);

const { scrollRef, showCenterLogo } = setupHeader();

const openDrawer = () => {
  drawerIsOpen.value = true;
};

const closeDrawer = () => {
  drawerIsOpen.value = false;
};
</script>

<style lang="scss" scoped>
@use "@/assets/base.scss" as *;
@use "@/assets/mixins.scss" as *;
@use "@/assets/variables.scss" as *;

.header {
  position: fixed;
  display: grid;
  grid-template-columns: 1fr 1fr;
  width: 100%;
  z-index: 15;
  justify-items: space-between;
  padding-top: 0.5rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  height: $header_height; //TODO: smaller header, use $header_height_mobile and all calcs for min height need to be responsive
  box-sizing: border-box;
}

.header-background {
  background-color: $sapphire_blue;
}

.header-logo {
  justify-self: flex-start;
  transition: all 2s ease;

  :deep(img) {
    width: 7rem;
    height: auto;
  }
}

.burger-button {
  justify-self: flex-end;
  max-width: 3rem;
  grid-column: 2;
  color: #d4af37;
  // margin-right: 1.2rem;
}

.center-logo {
  position: fixed;
  top: 20vh;
  left: 50%;
  transform: translateX(-50%);
  width: 14rem;
  z-index: 101;
  transition: all 2s ease;

  :deep(img) {
    width: 100%;
    transition: 2s;
  }
}

.item a {
  font-weight: 600;
  font-size: 24px;
}

.locale-button {
  background: none;
  color: $white;
  border: none;
  padding: 0;
  cursor: pointer;
  font-size: 24px;
  margin-top: 0.6rem;
  margin-left: 1rem;
}
</style>
