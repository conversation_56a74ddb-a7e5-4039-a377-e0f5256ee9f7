<template>
  <section aria-describedby="contact-us-info-header" class="contact-us-info">
    <icon-wrapper :name="iconName" scale="2.5" color="#dadada" />
    <h2 id="contact-us-info-header">{{ header }}:</h2>
    <div class="info-text">
      <p v-for="(paragraph, index) in paragraphs" :key="index">
        {{ translateParagraphs ? $t(paragraph) : paragraph }}
      </p>
    </div>
  </section>
</template>

<script lang="ts" setup>
defineOptions({
  name: "ContactInfo",
});
defineProps<{
  iconName: string;
  header: string;
  translateParagraphs: boolean;
  paragraphs: string[];
}>();
</script>

<style lang="scss" scoped>
@use "@/assets/base.scss" as *;
@use "@/assets/variables.scss" as *;
@use "@/assets/mixins.scss" as *;

.contact-us-info {
  text-align: center;
}

h2 {
  font-size: 1.2rem;
  margin-bottom: 0.3rem;
  color: $yellow;
}

p {
  color: $white;
  padding-bottom: 0.2rem;
}
</style>
