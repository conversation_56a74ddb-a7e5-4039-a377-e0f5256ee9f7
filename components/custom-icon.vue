<template>
  <client-only>
    <component
      :is="iconComponent"
      :name="name"
      :scale="scale"
      :color="color"
      :animation="animation"
      :hover="hover"
      :speed="speed"
      :flip="flip"
      :fill="fill"
      :label="label"
      :title="title"
      :inverse="inverse"
    />
  </client-only>
</template>

<script lang="ts" setup>
import { OhVueIcon } from "oh-vue-icons";

defineProps({
  name: {
    type: String,
    required: true
  },
  scale: {
    type: [Number, String],
    default: 1
  },
  color: {
    type: String,
    default: undefined
  },
  animation: {
    type: String,
    default: undefined
  },
  hover: {
    type: Boolean,
    default: false
  },
  speed: {
    type: String,
    default: undefined
  },
  flip: {
    type: String,
    default: undefined
  },
  fill: {
    type: String,
    default: undefined
  },
  label: {
    type: String,
    default: undefined
  },
  title: {
    type: String,
    default: undefined
  },
  inverse: {
    type: Boolean,
    default: false
  }
});

const iconComponent = OhVueIcon;
</script>
