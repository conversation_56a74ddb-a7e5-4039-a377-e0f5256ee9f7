<template>
  <div class="cards">
    <template v-for="(card, index) in allCards" :key="index">
      <home-card :image="{ src: card.imageSrc, alt: $t(card.imageAlt) }" :card="{
        header: $t(card.cardHeader),
        text: $t(card.cardText),
        additionalText: $t(card.cardAdditionalText),
      }" :link="{ text: $t(card.linkText), href: card.linkHref }" :card-index="index" :is-new="card.isNew">
        <slot v-if="index == 4">
          <div class="airbnb-links">
            <a class="nav__link" href="https://www.airbnb.com/rooms/806308046534973154">
              {{ $t("home.tours.cabin.cottage1") }}
            </a>
            <a class="nav__link" href="https://www.airbnb.co.uk/rooms/934605024384982028">
              {{ $t("home.tours.cabin.cottage2") }}
            </a>
          </div>
        </slot>
      </home-card>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { cardsInfo } from "@/assets/tours-info/home-cards-info";

defineOptions({
  name: "HomeCards",
});

const allCards = ref(cardsInfo);

</script>

<style lang="scss" scoped>
@use "@/assets/variables.scss" as *;
@use "@/assets/mixins.scss" as *;

.cards {
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  flex-grow: -1;
  margin-bottom: 5rem;
}

.nav__link {
  margin-top: 0.6rem;
  color: $blue;

  a {
    color: $card_link_color;
    font-weight: bold;
    padding: 0 0.5rem;

    &:hover {
      color: $card_link_color__hover;
    }
  }

  @include for-tablet-landscape-down {
    +.nav__link {
      margin-bottom: 3.5rem;
    }
  }
}

.airbnb-links {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.2rem;
  padding-left: 1rem;
  width: 100%;
  text-align: center;

  a {
    margin-top: 0.8rem;
    color: $card_link_color;
    font-weight: bold;
    padding: 0.5rem;
    display: inline-block;

    &:hover {
      color: $card_link_color__hover;
      cursor: pointer;
    }
  }

  .nav__link {
    margin-top: 0.6rem;
    color: $blue;
    text-align: center;
  }
}
</style>
