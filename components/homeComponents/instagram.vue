<template>
  <div class="instagram-feed-container">
    <client-only>
      <BeholdWidget feedId="cDjxN13pDqwQdtEH9oOt" @load="onWidgetLoad" />
    </client-only>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import BeholdWidget from "@behold/vue";

defineOptions({
  name: "InstagramFeed",
});

const isLoaded = ref(false);

const onWidgetLoad = () => {
  isLoaded.value = true;
  console.log("Instagram feed loaded successfully");
};
</script>

<style lang="scss" scoped>
.instagram-feed-container {
  width: 100%;
  min-height: 200px; // Prevent layout shift during loading
}
</style>
