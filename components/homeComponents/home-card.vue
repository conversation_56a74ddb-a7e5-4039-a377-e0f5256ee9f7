<template>
  <div :class="[
    'tour',
    { 'row-reverse': reverseFlow },
    { 'custom-tours': cardIndex === 2 },
    { cabin: cardIndex === 3 },
    { buggy: cardIndex === 4 },
  ]">
    <div :class="['tour--image', { 'border-reverse': reverseFlow }]">
      <CldImage :src="image.src" :alt="image.alt" width="600" height="400" crop="fill" gravity="auto" loading="lazy" />
      <div v-if="isNew" class="new-label">New</div>
    </div>
    <div :class="[
      'tour--text',
      { 'border-reverse': reverseFlow },
      { gradient0: cardIndex % 2 === 0 },
      { gradient1: cardIndex % 2 !== 0 },
    ]">
      <h3>{{ card.header }}</h3>
      <p>{{ card.text }}</p>
      <p>
        <span v-if="card.additionalText">{{ card.additionalText }} </span>
        <nuxt-link v-if="link.href" class="router-link" :to="link.href">
          {{ link.text ? link.text : $t("shared.links.readMore") }}
        </nuxt-link>
      </p>
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";

// Define interfaces locally to avoid import issues
interface TourImage {
  src: string;
  alt: string;
}

interface TourCardText {
  header: string;
  text: string;
  additionalText: string;
}

interface TourLink {
  text: string;
  href: string;
}

defineOptions({
  name: "HomeCard",
});

const props = defineProps<{
  image: TourImage;
  card: TourCardText;
  link: TourLink;
  cardIndex: number;
  isNew?: boolean;
}>();

const reverseFlow = computed(() => {
  return props.cardIndex % 2 !== 0;
});
</script>

<style lang="scss" scoped>
@use "@/assets/variables.scss" as *;
@use "@/assets/mixins.scss" as *;

.tour {
  width: 100%;
  display: flex;
  flex-direction: column;

  +.tour {
    margin-top: 5rem;
  }

  h3 {
    text-align: center;
    padding-top: 0.5rem;
    padding-bottom: 1rem;
    border-bottom: 4px solid $blue;
    color: $card_header_color;
  }

  p {
    padding: 1rem;
    color: $card_text_color;

    +p {
      padding-top: 0;
    }
  }

  .tour--image {
    height: 20rem;
    width: 94%;
    margin: auto;
    margin-bottom: 0.4rem;
    position: relative;

    :deep(img) {
      width: 100%;
      height: 100%;
      border-radius: 0 25% 0 0;
      object-fit: cover;
    }

    /* New label styling */
    .new-label {
      /* Positioning */
      position: absolute;
      top: 30px;
      left: -15px;
      /* Default position for small screens */

      /* Background and colors */
      background: linear-gradient(to bottom,
          #ff5a5f,
          #e63946);
      /* Red glossy gradient */
      color: white;

      /* Text styling */
      font-weight: bold;
      font-size: 0.7rem;
      text-transform: uppercase;
      letter-spacing: 1px;
      text-align: center;

      /* Dimensions and padding */
      padding: 0.3rem 0;
      width: 60px;
      /* Shorter length as requested */

      /* 3D and visual effects */
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.5);
      border: 1px solid rgba(255, 255, 255, 0.3);
      /* Subtle white border for depth */

      /* Rotation for diagonal appearance */
      transform-origin: top left;
      transform: rotate(-45deg);

      /* Other */
      z-index: 1;
      overflow: hidden;

      /* Animation */
      animation: pulse 2s infinite;

      /* Distinctive corner shape with varied border radius */
      border-top-left-radius: 40%;
      border-top-right-radius: 40%;
      border-bottom-left-radius: 10%;
      border-bottom-right-radius: 12%;
    }
  }

  .tour--text {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    background-color: $dark_grey;

    &.gradient0 {
      @include gold-bg-gradient(20deg);
    }

    &.gradient1 {
      @include gold-bg-gradient;
    }

    margin-left: 3%;
    margin-right: 3%;
    border-radius: 0 0 0 25%;
  }

  .router-link {
    color: $card_link_color;
    font-weight: bold;
    margin-bottom: 1rem;
    justify-self: flex-end;

    &:hover {
      color: $card_link_color__hover;
    }
  }

  @include for-tablet-landscape-up {
    flex-direction: row;
    max-width: 1024px;

    &.row-reverse {
      flex-direction: row-reverse;
    }

    .tour--text {
      padding: 1rem;
      width: 60%;
      border-radius: 0% 25%;
      margin-right: 0;
      margin-left: 2%;

      &.border-reverse {
        border-radius: 25% 0%;
        margin-right: 2%;
        margin-left: 0;
      }
    }

    .tour--image {
      height: auto;
      width: 40%;
      margin: 0;

      :deep(img) {
        border-radius: 25% 0;
      }

      &.border-reverse {
        :deep(img) {
          border-radius: 0 25%;
        }

        /* For large screens with reversed flow, position the label on the left */
        .new-label {
          left: -15px;
          right: auto;
          transform-origin: top left;
          transform: rotate(-45deg);
        }
      }

      /* For large screens, position the label on the right for even cards */
      .new-label {
        left: auto;
        right: -15px;
        transform-origin: top right;
        transform: rotate(45deg);
      }
    }

    &.custom-tours {
      max-height: 22rem;
    }

    &.cabin {
      max-height: 18rem;
    }

    &.buggy {
      max-height: 22rem;
    }

    .router-link {
      margin-bottom: 0;
    }
  }
}

/* Add pulse animation for the new-label */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 57, 70, 0.7);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(230, 57, 70, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(230, 57, 70, 0);
  }
}
</style>
