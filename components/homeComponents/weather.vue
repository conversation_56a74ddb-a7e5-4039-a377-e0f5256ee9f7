<template>
  <div class="weather">
    <img :src="svgPath" :alt="weather.symbol_code" />
    <p class="celsius">
      {{ weather.air_temperature }}
      <icon-wrapper name="wi-celsius" scale="2" />
    </p>
    <p>
      {{ weather.wind_speed }} m/s
      <icon-wrapper id="windIcon" name="wi-direction-up" />
    </p>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from "vue";

import { fetchWeather } from "@/composables/fetchWeather";
import { IWeather } from "@/interfaces/weather";
export default defineComponent({
  name: "WeatherDisplay",
  async setup() {
    onMounted(() => {
      document.getElementById(
        "windIcon"
      )!.style.rotate = `${weather.value.wind_from_direction}deg`;
    });
    const tempWeather: IWeather = await fetchWeather();
    const weather = ref(tempWeather);

    const svgPath = computed(() => {
      return `/weather-icons/svg/${weather.value.symbol_code}.svg`;
    });

    return { weather, svgPath };
  },
});
</script>

<style lang="scss" scoped>
@use "@/assets/base.scss" as *;
@use "@/assets/mixins.scss" as *;

.weather {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: space-around;
  row-gap: 0.8rem;

  @include for-tablet-portrait-down {
    padding-top: 1rem;
    p {
      padding-left: 2rem;
    }
  }

  @include for-tablet-portrait-up {
    flex-direction: column;
    align-items: flex-end;
  }

  img {
    width: 3rem;
    height: 3rem;
  }

  .celsius {
    svg {
      margin-left: -10px;
      vertical-align: middle;
    }
  }
}
</style>
