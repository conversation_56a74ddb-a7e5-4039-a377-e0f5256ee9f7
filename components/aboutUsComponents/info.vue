<template>
  <aside class="info-wrapper">
    <h1>{{ $t("aboutUs.info-header") }}</h1>

    <div class="staff">
      <h2>Vignir</h2>
      <div class="content-wrapper">
        <CldImage class="staff-image" src="vignir_mvayni" width="70" height="70" sizes="(min-width: 900px) 100px, 70px"
          crop="fill" gravity="face" loading="lazy" alt="Vignir" />
        <p>{{ $t("aboutUs.vignir") }}</p>
      </div>
    </div>

    <div class="staff">
      <h2>Kolfinna</h2>
      <div class="content-wrapper">
        <CldImage class="staff-image" src="kolfinna_m6shks" width="70" height="70"
          sizes="(min-width: 900px) 100px, 70px" crop="fill" gravity="face" loading="lazy" radius="50" alt="Kolfinna" />
        <p>{{ $t("aboutUs.kolfinna") }}</p>
      </div>
    </div>
  </aside>
</template>

<script lang="ts" setup>
defineOptions({
  name: "AboutInfo",
});
</script>

<style lang="scss" scoped>
@use "@/assets/base.scss" as *;
@use "@/assets/variables.scss" as *;
@use "@/assets/mixins.scss" as *;

.info-wrapper {
  padding: 2rem;
  background-color: $dark_grey;
  margin-top: 1rem;

  @include for-tablet-landscape-up {
    min-height: calc(100vh - $header_height - $footer_height - 3rem);
    padding-bottom: unset;
  }

  h1,
  h2 {
    color: $yellow;
  }

  h2 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
  }

}

.staff {
  margin-bottom: 1.5rem;

  .content-wrapper {
    position: relative;
    overflow: hidden;
  }


  .staff-image {
    border-radius: 50%;
    shape-outside: polygon(50% 0, 100% 50%, 50% 100%, 0 50%);
    width: 70px;
    height: 70px;
    float: left;
    top: 10px;
    margin-right: 1rem;

    &.right {
      float: right;
      margin-right: 0;
      margin-left: 1rem;
      shape-outside: circle(35px at 35px 35px);
    }
  }

  p {
    margin: 0;
    color: $white;
    display: inline;
    line-height: 1.5;
  }

  @include for-tablet-landscape-up {
    .staff-image {
      width: 100px;
      height: 100px;
      // shape-outside: polygon(50% 0, 100% 50%, 50% 100%, 0 50%);

      &.right {
        shape-outside: circle(50px at 50px 50px);
      }
    }
  }
}
</style>
