<template>
  <div>
    <!-- Facebook Customer Chat Plugin -->
    <div class="fb-customerchat" :attribution="attribution" :page_id="pageId" :theme_color="themeColor"></div>
  </div>
</template>

<script setup>
const config = useRuntimeConfig()

// Props with defaults
const props = defineProps({
  attribution: {
    type: String,
    default: 'setup_tool'
  },
  themeColor: {
    type: String,
    default: '#0084ff'
  },

})

// Get page ID from runtime config
const pageId = config.public.facebookPageId
console.log({ pageId });


// Re-parse Facebook elements when component mounts
onMounted(() => {
  if (process.client && window.FB) {
    window.FB.XFBML.parse()
  }
})
</script>