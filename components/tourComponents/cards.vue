<template>
  <div class="cards--container">
    <div class="cards">
      <template v-for="(card, index) in allTourCards" :key="index">
        <card :image="card.imageSrc" :card="{ header: $t(card.cardHeader), text: $t(card.cardText) }"
          :link="{ text: card.linkText, href: card.linkHref }"
          :info="{ duration: card.duration, departure: card.departure }" :card-index="index" />
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from "vue";
import { tourCards } from "@/assets/tours-info/tour-cards-info";
import Card from "@/components/tourComponents/tour-card.vue";
export default defineComponent({
  name: "TourCards",
  components: { Card },
  setup() {
    const allTourCards = ref(tourCards);
    return { allTourCards };
  },
});
</script>

<style lang="scss" scoped>
@use "@/assets/base.scss" as *;
@use "@/assets/mixins.scss" as *;

.cards--container {
  margin-top: 2rem;
}

.cards {
  display: grid;
  grid-template-columns: 1fr;
  justify-items: center;

  @include for-tablet-portrait-down {
    .card {
      margin-bottom: 2rem;
    }
  }

  @include for-desktop-up {
    width: 95%;
    margin: auto;
    grid-template-columns: 1fr 1fr;
  }

  @include for-big-desktop-up {
    width: 70%;
    margin: auto;
    grid-template-columns: 1fr 1fr;
  }
}
</style>
