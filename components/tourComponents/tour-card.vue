<template>
  <div class="card">
    <div :class="['card__wrapper', { 'row-reverse': cardIndex % 2 !== 0 }]">
      <div class="card__image">
        <CldImage :src="image" width="600" height="400" crop="fill" gravity="auto" loading="lazy" :alt="card.header" />
      </div>
      <div class="card__content">
        <h2>{{ card.header }}</h2>
        <p>{{ card.text }}</p>
        <nav class="nav__link">
          <nuxt-link :to="link.href">{{ $t(link.text) }}</nuxt-link>
        </nav>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// Define types for props
type CardTextType = {
  header: string;
  text: string;
};

type CardLinkType = {
  text: string;
  href: string;
};

type CardInfoType = {
  duration: string;
  departure: string;
};

const props = defineProps({
  image: {
    required: true,
    type: String,
    default: "",
  },
  card: {
    required: true,
    type: Object as () => CardTextType,
  },
  link: {
    required: true,
    type: Object as () => CardLinkType,
  },
  info: {
    required: true,
    type: Object as () => CardInfoType,
  },
  cardIndex: {
    type: Number,
    required: true,
  },
});

// No need for computed property as we're using inline calculation in the template
</script>

<style lang="scss" scoped>
@use "@/assets/base.scss" as *;
@use "@/assets/variables.scss" as *;
@use "@/assets/mixins.scss" as *;

.card {
  height: 100%;
  width: 100%;
  box-sizing: border-box;

  @include for-tablet-portrait-up {
    height: 20rem;
    width: 35rem;
    margin-bottom: 2rem;
  }

  h2 {
    font-size: 18px;
    color: $card_header_color;
  }

  p {
    font-size: 15px;
    color: black;
  }
}

$card-height-lg-screen: 20rem;

.card__wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;

  @include for-tablet-portrait-up {
    flex-direction: row;

    &.row-reverse {
      flex-direction: row-reverse;
    }
  }
}

.card__image {
  width: 90%;
  box-sizing: border-box;
  position: relative;

  @include for-tablet-portrait-down {
    height: 15rem;
  }

  @include for-tablet-portrait-up {
    width: 20rem;
    height: $card-height-lg-screen;
  }

  :deep(img) {
    width: 100%;
    height: 100%;
    object-fit: cover;

    @include for-tablet-portrait-up {
      border-start-start-radius: 25%;

      .row-reverse & {
        border-start-start-radius: 0;
        border-start-end-radius: 25%;
      }
    }
  }
}

.card__content {
  display: grid;
  grid-template-columns: 1fr;
  width: 90%;
  background-color: $dark_grey;
  @include gold-bg-gradient(40deg);
  padding: 1rem;
  box-sizing: border-box;

  @include for-tablet-portrait-up {
    width: 20rem;
    height: $card-height-lg-screen;
    border-end-end-radius: 25%;

    .row-reverse & {
      border-end-end-radius: 0;
      border-end-start-radius: 25%;
    }
  }

  .card__info {
    margin-top: 1rem;
    display: flex;
    justify-content: space-evenly;
  }
}

.nav__link {
  margin-top: 0.6rem;
  text-align: center;
  align-self: self-end;
  font-weight: bold;

  a {
    color: $card_link_color;

    &:hover {
      color: $card_link_color__hover;
    }
  }
}
</style>
