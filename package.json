{"name": "nuxt-app", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "dev:fast": "nuxt dev --no-clear --no-fork", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint:js": "eslint --ext \".ts,.vue\" --ignore-path .gitignore .", "lint:prettier": "prettier --check .", "lint": "npm run lint:js && npm run lint:prettier", "lintfix": "prettier --write --list-different", "start": "node .output/server/index.mjs"}, "devDependencies": {"@nuxtjs/device": "^3.2.4", "@nuxtjs/eslint-config-typescript": "^12.0.0", "@nuxtjs/i18n": "^9.5.4", "@nuxtjs/sitemap": "^7.2.10", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "nuxt": "^3.17.1", "nuxt-facebook-chat": "^1.0.5", "nuxt-seo-kit": "^1.3.13", "prettier": "^3.5.3", "sass": "^1.72.0", "sass-loader": "^14.1.1", "typescript": "^5.8.3"}, "dependencies": {"@behold/vue": "^0.1.7", "@nuxtjs/cloudinary": "^4.0.0", "@nuxtjs/robots": "^5.2.10", "@nuxtjs/seo": "^3.0.3", "click-outside-vue3": "^4.0.1", "consola": "^3.4.2", "defu": "^6.1.4", "nuxt-mail": "^6.0.1", "nuxt-site-config": "^3.1.9", "oh-vue-icons": "^1.0.0-rc3"}}