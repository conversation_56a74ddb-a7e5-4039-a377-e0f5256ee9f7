# SEO Setup for Glacier Paradise

This document outlines the comprehensive SEO setup implemented for the Glacier Paradise website.

## Overview

The SEO setup includes:
- ✅ Enhanced meta tags (title, description, Open Graph, Twitter Cards)
- ✅ Structured data (JSON-LD) for organization and tours
- ✅ Canonical URLs
- ✅ Hreflang tags for internationalization
- ✅ Sitemap generation
- ✅ Robots.txt configuration
- ✅ Global SEO composable for consistent implementation

## Modules Installed

### @nuxtjs/seo
A comprehensive SEO module that includes:
- `@nuxtjs/sitemap` - Automatic sitemap generation
- `@nuxtjs/robots` - Robots.txt management
- `nuxt-site-config` - Site configuration
- `nuxt-og-image` - Open Graph image generation
- `@unhead/schema-org` - Structured data management

## Implementation

### 1. Global SEO Configuration (app.vue)

The `app.vue` file sets up:
- Global title template
- Organization structured data
- LocalBusiness structured data
- Language-specific meta tags

### 2. SEO Composable (composables/useSeo.ts)

A reusable composable that provides:
- Consistent SEO meta tag setup
- Open Graph and Twitter Card configuration
- Canonical URL generation
- Hreflang tag management
- Structured data integration

#### Usage Example:
```typescript
useSeo({
  title: 'Page Title',
  description: 'Page description',
  image: 'https://example.com/image.jpg',
  type: 'article',
  structuredData: createTourSchema({
    name: 'Tour Name',
    description: 'Tour description',
    // ... other tour data
  }),
})
```

### 3. Structured Data Schemas

Pre-built schema functions for:
- **Organization Schema**: Company information
- **LocalBusiness Schema**: Business details with location
- **Tour Schema**: Individual tour information

### 4. Page-Specific SEO

Each page uses the `useSeo()` composable with appropriate:
- Page titles
- Meta descriptions
- Open Graph images
- Structured data (for tour pages)

## SEO Features Implemented

### Meta Tags
- Title tags with template
- Meta descriptions
- Open Graph tags (title, description, image, URL, type, site name, locale)
- Twitter Card tags
- Robots meta tag
- Author meta tag
- Theme color meta tag

### Technical SEO
- Canonical URLs for all pages
- Hreflang tags for English and Icelandic
- Proper HTML lang attribute
- Structured data for better search engine understanding

### International SEO
- Language-specific meta tags
- Hreflang implementation
- Locale-aware Open Graph tags

## Configuration

### Nuxt Config (nuxt.config.ts)
```typescript
modules: [
  "@nuxtjs/i18n",
  "@nuxtjs/device",
  ...(isDev && process.env.LOAD_ALL_MODULES !== 'true' ? [] : [
    "@nuxtjs/sitemap",
    "nuxt-site-config",
    "@nuxtjs/robots",
    "@nuxtjs/seo",
  ]),
  "nuxt-mail",
  "@nuxtjs/cloudinary",
],
```

### Runtime Config
Site information is stored in `runtimeConfig.public`:
- `siteUrl`: https://www.glacierparadise.is
- `siteName`: Glacier Paradise
- `siteDescription`: Main site description

## Testing SEO

### Development Testing
1. Run `npm run dev`
2. Visit pages and check:
   - Page titles in browser tabs
   - View source to verify meta tags
   - Check `/sitemap.xml` for sitemap generation
   - Check `/robots.txt` for robots configuration

### Production Testing
1. Use tools like:
   - Google Search Console
   - Facebook Sharing Debugger
   - Twitter Card Validator
   - Rich Results Test (Google)
   - Lighthouse SEO audit

## Best Practices Implemented

1. **Unique Titles**: Each page has a unique, descriptive title
2. **Meta Descriptions**: Compelling descriptions under 160 characters
3. **Open Graph Images**: Optimized images for social sharing
4. **Structured Data**: Rich snippets for better search results
5. **Mobile-Friendly**: Responsive design with proper viewport
6. **Fast Loading**: Optimized images and code splitting
7. **Clean URLs**: SEO-friendly URL structure
8. **Internal Linking**: Proper navigation structure

## Monitoring and Maintenance

### Regular Tasks
- Monitor Google Search Console for errors
- Update structured data as needed
- Check for broken links
- Monitor page load speeds
- Update meta descriptions for better CTR

### Analytics Integration
- Beam Analytics is already integrated
- Consider adding Google Analytics 4
- Set up Google Search Console
- Monitor Core Web Vitals

## Future Enhancements

1. **Rich Snippets**: Add more structured data types
2. **FAQ Schema**: Add FAQ structured data for common questions
3. **Review Schema**: Add customer review structured data
4. **Breadcrumbs**: Implement breadcrumb navigation
5. **AMP Pages**: Consider AMP for mobile performance
6. **Schema Markup**: Expand structured data coverage

## Troubleshooting

### Common Issues
1. **Missing Meta Tags**: Check if `useSeo()` is called in page setup
2. **Incorrect Canonical URLs**: Verify site URL in runtime config
3. **Missing Structured Data**: Ensure structured data is properly formatted
4. **Hreflang Issues**: Check i18n configuration

### Debug Tools
- Nuxt DevTools SEO tab
- Browser developer tools
- Google Rich Results Test
- Facebook Sharing Debugger
