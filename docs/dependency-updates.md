# Nuxt and Core Dependencies Update

## Updates Made

### Nuxt Core
- Updated Nuxt to version 3.17.1 (latest stable version)

### Nuxt Modules
- Updated @nuxtjs/device from 3.1.0 to 3.2.4
- Updated @nuxtjs/i18n from ~9.5.4 to ^9.5.4 (removed tilde for more flexible versioning)
- Updated nuxt-mail from 4.0.1 to 6.0.1 (major version update)

### Development Dependencies
- Updated eslint from 8.48.0 to 8.56.0
- Updated eslint-config-prettier from 8.8.0 to 9.1.0
- Updated eslint-plugin-prettier from 4.2.1 to 5.1.3
- Updated prettier from 2.8.7 to 3.5.3 (major version update)
- Updated sass from 1.69.5 to 1.72.0
- Updated sass-loader from 13.3.2 to 14.1.1
- Updated typescript from ~5.3.3 to ^5.8.3

### Other Dependencies
- Updated defu from 6.1.2 to 6.1.4

## Configuration Changes

### nuxt-mail Configuration
- Moved nuxt-mail configuration from module options to runtimeConfig
- This change was necessary to support nuxt-mail v6.0.1
- The mail configuration is now in the runtimeConfig section of nuxt.config.ts

## Potential Issues to Watch For

1. **Prettier Major Version Update**: The update from Prettier v2 to v3 may cause formatting changes in your codebase. You might need to run `npm run lintfix` to apply the new formatting rules.

2. **ESLint Configuration**: The updates to eslint-config-prettier and eslint-plugin-prettier might require adjustments to your ESLint configuration if you have custom rules.

3. **nuxt-mail Usage**: If you're using nuxt-mail in your components, you'll need to update your code to use the new API:

   **Nuxt 3 Usage (v6.0.1):**
   ```vue
   <script setup>
   // Via composable
   const mail = useMail()
   
   mail.send({
     from: 'John Doe',
     subject: 'Subject',
     text: 'Message content',
   })
   
   // Or via injected variable
   const { $mail } = useNuxtApp()
   
   $mail.send({
     from: 'John Doe',
     subject: 'Subject',
     text: 'Message content',
   })
   </script>
   ```

## Next Steps

1. Test all functionality that uses the updated dependencies, especially forms that send emails using nuxt-mail.
2. Run linting and formatting tools to ensure code consistency with the updated tools.
3. Consider updating other dependencies in the future to keep the project up-to-date.
