# Cloudinary Migration Plan

This document outlines the plan for migrating images from the local project to Cloudinary, including the organization structure and tracking of migration progress.

## Cloudinary Organization Structure

Based on the current project structure and image usage, we recommend organizing images on Cloudinary as follows:

### Folder Structure

```
glacier-paradise/
├── backgrounds/
│   └── (large background images used for page headers and scrolling effects)
├── tours/
│   ├── snow-cat/
│   │   └── (images specific to the snow cat tour)
│   ├── midnight-sun/
│   │   └── (images specific to the midnight sun tour)
│   └── cabin/
│       └── (images specific to the cabin)
├── cards/
│   └── (card thumbnail images used on the homepage and tours page)
├── team/
│   └── (staff/team member photos)
├── logos/
│   └── (company logos in various formats)
└── certificates/
    └── (license and certification images)
```

### Transformation Presets

We recommend setting up the following transformation presets in Cloudinary:

1. **responsive_background** - For large background images

   - Responsive sizing
   - Progressive loading
   - Format auto (WebP/AVIF with fallbacks)

2. **card_thumbnail** - For tour card images

   - Fixed aspect ratio (16:9)
   - Optimized for display in cards
   - Format auto

3. **logo** - For logo images

   - Preserve transparency
   - Format auto

4. **team_photo** - For team member photos
   - Standard sizing
   - Face detection for optimal cropping
   - Format auto

## Image Migration Tracking

| Current Path                                    | Image Description                         | Purpose                                        | Cloudinary Folder                    | Cloudinary Asset ID                      | Status |
| ----------------------------------------------- | ----------------------------------------- | ---------------------------------------------- | ------------------------------------ | ---------------------------------------- | ------ |
| `/images/iskyunum1_tinified.avif`               | Large panoramic background                | Homepage scrolling background                  | glacier-paradise/backgrounds/        | iskyunum1_tinified_viuayc                | To Do  |
| `/images/iskyunum1_tinified.png`                | Large panoramic background (PNG fallback) | Homepage scrolling background for Edge browser | glacier-paradise/backgrounds/        | iskyunum1_tinified_ou4am9                | To Do  |
| `/images/topoftthediamond-resized.card.jpg`     | Snow Cat Tour thumbnail                   | Homepage and tours page card                   | glacier-paradise/cards/              | topoftthediamond-resized.card_fnwonh     | To Do  |
| `/images/midnightsun-resized.card.jpg`          | Midnight Sun Tour thumbnail               | Homepage and tours page card                   | glacier-paradise/cards/              | midnightsun-resized.card_ymp4gi          | To Do  |
| `/images/cabin_northern_lights-resized.jpg`     | Cabin thumbnail                           | Homepage card                                  | glacier-paradise/cards/              | cabin_northern_lights-resized_qs1nc6     | To Do  |
| `/images/custom_card.jpg`                       | Custom Tour thumbnail                     | Homepage card                                  | glacier-paradise/cards/              | custom_card_fn02v8                       | To Do  |
| `/images/toppur_panorama-resized.jpg`           | Panoramic view from top                   | Tour page header                               | glacier-paradise/tours/snow-cat/     | toppur_panorama-resized_xiqeu2           | To Do  |
| `/images/midnightsun.sida.jpg`                  | Midnight sun tour image                   | Tour page header                               | glacier-paradise/tours/midnight-sun/ | midnightsun.sida_ui1b7f                  | To Do  |
| `/images/fra-breiduvik-cropped-2.jpg`           | View from Breiduvik                       | About Us page header                           | glacier-paradise/backgrounds/        | fra-breiduvik-cropped-2_etbjm4           | To Do  |
| `/images/vignir.jpg`                            | Team member photo                         | About Us page                                  | glacier-paradise/team/               | vignir_mvayni                            | To Do  |
| `/images/kolfinna.jpg`                          | Team member photo                         | About Us page                                  | glacier-paradise/team/               | kolfinna_m6shks                          | To Do  |
| `/images/day-tour-license.jpg`                  | Tour license certificate                  | Tour pages                                     | glacier-paradise/certificates/       | day-tour-license_qgauhn                  | To Do  |
| `/images/trodararnir.jpg`                       | Snow cat vehicle                          | Tour content                                   | glacier-paradise/tours/snow-cat/     | trodararnir_qkasrb                       | To Do  |
| `/images/trodararnir-resized.jpg`               | Snow cat vehicle (resized)                | Tour content                                   | glacier-paradise/tours/snow-cat/     | trodararnir-resized_eruiuv               | To Do  |
| `/images/trodarinn-a-toppnum.jpg`               | Snow cat at the top                       | Tour content                                   | glacier-paradise/tours/snow-cat/     | trodarinn-a-toppnum_jestur               | To Do  |
| `/images/2022-084.jpg`                          | Landscape photo                           | Tour content                                   | glacier-paradise/backgrounds/        | 2022-084_d5pfas                          | To Do  |
| `/logos/logo-glacier-paradise-transparent.png`  | Company logo (PNG)                        | Header logo                                    | glacier-paradise/logos/              | logo-glacier-paradise-transparent_lyah7h | To Do  |
| `/logos/logo-glacier-paradise-transparent.avif` | Company logo (AVIF)                       | Header logo                                    | glacier-paradise/logos/              | logo-glacier-paradise-transparent_svecow | To Do  |
| `/logos/facebook-logo.png`                      | Facebook logo                             | Social media links                             | glacier-paradise/logos/              | facebook-logo_fh8dtk                     | To Do  |
| `/logos/instagram-logo.png`                     | Instagram logo                            | Social media links                             | glacier-paradise/logos/              | instagram-logo_h3kuhd                    | To Do  |
| `/weather-icons/svg/*`                          | Weather icons                             | Weather display                                | glacier-paradise/weather-icons/      |                                          | To Do  |

## Implementation Plan

### 1. Set Up Cloudinary Account and Structure

- Create folders according to the structure above
- Set up transformation presets
- Configure upload presets for easier asset management

### 2. Upload Images to Cloudinary

- Upload all images to their respective folders
- Apply appropriate tags for easier searching
- Document Cloudinary Asset IDs in this tracking sheet

### 3. Update Code to Use Cloudinary URLs

- Create a utility function for Cloudinary URLs
- Replace hardcoded image paths with Cloudinary URLs
- Implement responsive images using Cloudinary's responsive features

### 4. Testing

- Test all pages to ensure images load correctly
- Test responsive behavior across different devices
- Test fallback mechanisms for older browsers

## Cloudinary Integration for the Large Background Image

The large background image on the landing page (`/images/iskyunum1_tinified.avif` and its PNG fallback) that has a transition effect should be fetched from Cloudinary. Here's how to implement it:

1. **Upload both versions to Cloudinary** in the backgrounds folder
2. **Use Cloudinary's format auto feature** to automatically serve the optimal format (AVIF, WebP, or PNG) based on browser support
3. **Implement responsive delivery** using Cloudinary's responsive features
4. **Update the CSS in `scrolling-bg-image.vue`** to use Cloudinary URLs instead of local files
5. **Maintain the animation effect** as it's controlled by CSS, not by the image source

Example implementation:

```scss
.scrolling-bg-image {
  // Other properties remain the same
  background-image: url("https://res.cloudinary.com/your-cloud-name/image/upload/f_auto,q_auto/v1/glacier-paradise/backgrounds/iskyunum1_tinified");

  // No need for separate PNG fallback as Cloudinary handles format detection
}
```

This approach will:

- Optimize delivery based on the user's device and browser
- Reduce bandwidth usage with automatic format selection
- Maintain the same visual experience and animation effects
- Allow for easier image updates in the future
