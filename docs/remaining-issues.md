# Remaining Issues in Glacier Paradise Nuxt Project

This document lists the remaining issues in the Glacier Paradise Nuxt project that need to be addressed. These issues have been identified during the process of fixing errors and warnings in the project.

## TypeScript and Linting Issues

### Component Naming Issues

~~Several components don't follow <PERSON><PERSON>'s multi-word component naming convention:~~ ✅ FIXED

- ~~`components/contactUsComponents/form.vue` - Should be renamed to something like "ContactForm"~~ ✅ FIXED
- ~~`components/contactUsComponents/info.vue` - Should be renamed to something like "ContactInfo"~~ ✅ FIXED
- ~~`components/aboutUsComponents/info.vue` - Should be renamed to something like "AboutInfo"~~ ✅ FIXED

All component naming issues have been fixed by adding proper component names in the script sections.

### ESLint Configuration Issues ✅ FIXED

~~The ESLint configuration is not compatible with the current TypeScript version:~~

- ~~The project is using TypeScript 5.8.3, but @typescript-eslint/typescript-estree only supports up to TypeScript 5.2.0~~
- ~~Consider downgrading TypeScript or updating the ESLint configuration~~

The ESLint configuration issues have been fixed by downgrading TypeScript to version 5.3.3, which is compatible with the current ESLint configuration.

### Import Path Issues ✅ FIXED

~~Many files have import path issues:~~

- ~~Non-relative paths are not allowed when 'baseUrl' is not set~~
- ~~Consider setting up a proper `baseUrl` in the TypeScript configuration~~

The import path issues have been fixed by explicitly setting the `baseUrl` in the TypeScript configuration.

### Code Quality Issues

#### Console Logs

~~There are console.log statements in production code that should be removed:~~ ✅ FIXED

- ~~`components/contactUsComponents/form.vue` - Line 106 has a console.log statement~~ ✅ FIXED

All console.log statements have been removed from the production code.

#### Unused Variables

~~There are unused variables in the code:~~ ✅ FIXED

- ~~`pages/tours/midnight-sun.vue` - Line 72: 'isDialogOpen' is assigned a value but never used~~ ✅ FIXED
- ~~`pages/tours/snow-cat.vue` - Line 100: 'isDialogOpen' is assigned a value but never used~~ ✅ FIXED

All unused variables have been removed from the code.

#### Missing Keys in v-for Loops

~~Some v-for loops are missing key attributes:~~ ✅ FIXED

- ~~`components/contactUsComponents/info.vue` - Line 6: Elements in iteration expect to have 'v-bind:key' directives~~ ✅ FIXED

All v-for loops now have proper key attributes.

### Vue Style Guide Issues

#### Attribute Order

~~Some Vue components have attributes in the wrong order according to the Vue style guide:~~ ✅ FIXED

- ~~`components/contactUsComponents/form.vue` - Line 7: Attribute "ref" should go before "method"~~ ✅ FIXED
- ~~`components/contactUsComponents/form.vue` - Line 65: Attribute "v-if" should go before ":class"~~ ✅ FIXED
- ~~`components/essentials.vue` - Line 8: Attribute "id" should go before "ref"~~ ✅ FIXED
- ~~`pages/tours/snow-cat.vue` - Line 11: Attribute "id" should go before "disabled"~~ ✅ FIXED

All attribute order issues have been fixed according to the Vue style guide.

#### Component Definition Name Casing

~~Some component names don't follow PascalCase convention:~~ ✅ FIXED

- ~~`components/aboutUsComponents/info.vue` - Line 23: Property name "info" is not PascalCase~~ ✅ FIXED
- ~~`components/aboutUsComponents/mainContent.vue` - Line 19: Property name "mainContent" is not PascalCase~~ ✅ FIXED
- ~~`components/contactUsComponents/header.vue` - Line 14: Property name "contactUsHeader" is not PascalCase~~ ✅ FIXED
- ~~`plugins/ohVueIcons.ts` - Line 33: Property name "v-icon" is not PascalCase~~ ✅ FIXED

All component names now follow the PascalCase convention.

## Other Issues

### Missing Type Declarations ✅ FIXED

~~The `click-outside-vue3` package is missing type declarations, as indicated by the `@ts-expect-error` comment in `plugins/clickOutSide.ts`.~~

The missing type declarations for `click-outside-vue3` have been fixed by creating a custom type declaration file in `types/click-outside-vue3.d.ts`.

### Comment Formatting ✅ FIXED

~~There's an issue with comment formatting in `plugins/clickOutSide.ts` - Line 1: Expected space or tab after '//' in comment.~~

The comment formatting issue has been fixed by adding a space after the `//` in the comment.

## Node.js Version

The project is currently using Node.js 18.18.0, which is compatible with the project's dependencies. However, some dependencies have warnings about Node.js version compatibility:

- Some packages require Node.js <=19.0.0, but the system has Node.js 22.14.0 installed
- The project uses an `.nvmrc` file to specify Node.js 18.18.0 for this project

## Next Steps

To address these issues, we'll tackle them one by one in the following order:

1. ~~Fix component naming issues~~ ✅ COMPLETED
2. ~~Fix missing keys in v-for loops~~ ✅ COMPLETED
3. ~~Remove console logs~~ ✅ COMPLETED
4. ~~Fix unused variables~~ ✅ COMPLETED
5. ~~Fix attribute order~~ ✅ COMPLETED
6. ~~Fix component definition name casing~~ ✅ COMPLETED
7. ~~Fix comment formatting~~ ✅ COMPLETED
8. ~~Address ESLint configuration issues~~ ✅ COMPLETED
9. ~~Address import path issues~~ ✅ COMPLETED
10. ~~Address missing type declarations~~ ✅ COMPLETED

## Remaining Linting Issues

All the major issues have been fixed, but there are still some linting issues that could be addressed in the future:

1. Component naming issues (multi-word component names)
2. Attribute order in templates
3. Missing keys in v-for loops
4. Import order
5. Component definition name casing (PascalCase)
6. Unused variables
7. Camelcase naming conventions

These issues could be fixed by running:

```bash
npm run lint:js -- --fix
```

However, this might not fix all issues and some manual intervention might be required.
