# Testing Email Functionality

After updating the nuxt-mail package from v4.0.1 to v6.0.1, it's important to verify that the email functionality is working correctly. Follow these steps to test the contact form:

## Prerequisites

1. Make sure you have set up the required environment variables in your `.env` file:
   ```
   NUXT_EMAIL_TO=<EMAIL>
   NUXT_EMAIL_HOST=your-smtp-host
   NUXT_EMAIL_PORT=your-smtp-port
   NUXT_EMAIL_USERNAME=your-smtp-username
   NUXT_EMAIL_PASSWORD=your-smtp-password
   ```

2. Ensure the development server is running:
   ```bash
   npm run dev
   ```

## Testing Steps

1. Navigate to the Contact Us page at http://localhost:3000/contactUs
2. Fill out the contact form with test data:
   - Name: Test User
   - Email: <EMAIL>
   - Subject: Test Email
   - Message: This is a test message to verify the email functionality after updating nuxt-mail to v6.0.1.
3. Click the "Send" button
4. Verify that the success message appears
5. Check the email inbox specified in your NUXT_EMAIL_TO environment variable to confirm receipt of the test email

## Troubleshooting

If the email is not sent successfully:

1. Check the browser console for any error messages
2. Verify that your SMTP credentials are correct
3. Make sure your SMTP server allows connections from your IP address
4. Check if your email provider requires any additional security settings

## Implementation Details

The contact form uses the `useMail()` composable from nuxt-mail v6:

```vue
const mail = useMail();

const submitForm = async () => {
  try {
    submitStatus.value = "loading";
    await mail.send({
      replyTo: form.value?.email.value,
      subject: form.value?.subject.value,
      text: form.value?.message.value,
      html: `<h1>Nafn: ${form.value?.username.value}</h1><br><p>Netfang: ${form.value?.email.value}</p><br><p>Viðfangsefni: ${form.value?.subject.value}</p><br><p>Netfang: ${form.value?.message.value}</p>`,
    });
    submitStatus.value = "success";
  } catch (error) {
    // Error is silently caught and status is updated
    submitStatus.value = "error";
  }
};
```

The nuxt-mail configuration is now in the runtimeConfig section of nuxt.config.ts:

```js
runtimeConfig: {
  // ... other config
  mail: {
    message: {
      to: process.env.NUXT_EMAIL_TO,
    },
    smtp: {
      host: process.env.NUXT_EMAIL_HOST,
      port: process.env.NUXT_EMAIL_PORT,
      auth: {
        user: process.env.NUXT_EMAIL_USERNAME,
        pass: process.env.NUXT_EMAIL_PASSWORD,
      },
    },
  },
},
```
