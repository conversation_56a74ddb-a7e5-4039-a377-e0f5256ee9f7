# Using Cloudinary in the Glacier Paradise Project

This document provides instructions on how to use Cloudinary for image management in the Glacier Paradise website.

## Setup

1. Create a `.env` file in the root of the project based on the `.env.example` file:

   ```
   # Cloudinary credentials
   CLOUDINARY_CLOUD_NAME=your_cloud_name
   CLOUDINARY_API_KEY=your_api_key
   CLOUDINARY_API_SECRET=your_api_secret
   ```

2. Replace the placeholder values with your actual Cloudinary credentials.

## Using the CloudinaryImage Component

The project includes a `cloudinary-image` component that makes it easy to use Cloudinary images:

```vue
<template>
  <cloudinary-image
    path="glacier-paradise/backgrounds/iskyunum1_tinified"
    alt="Glacier panorama"
    :options="{ width: 1200, quality: 'auto', format: 'auto' }"
  />
</template>
```

### Component Props

- `path` (required): The path to the image in Cloudinary (without the cloud name)
- `options`: Cloudinary transformation options
- `alt`: Alt text for the image
- `width`: Width of the image
- `height`: Height of the image
- `loading`: Loading attribute (lazy, eager)
- `cssClass`: CSS class for the image

## Using Cloudinary Directly in CSS

For background images in CSS, you can use the `getCloudinaryUrl` utility function:

```vue
<template>
  <div class="background-image"></div>
</template>

<script setup>
import { getCloudinaryUrl, CloudinaryPresets } from "~/utils/cloudinary";

const backgroundImageUrl = getCloudinaryUrl(
  "glacier-paradise/backgrounds/iskyunum1_tinified",
  CloudinaryPresets.RESPONSIVE_BACKGROUND
);
</script>

<style scoped>
.background-image {
  background-image: v-bind("`url(${backgroundImageUrl})`");
  /* Other styles */
}
</style>
```

## Predefined Transformation Presets

The `CloudinaryPresets` object in `utils/cloudinary.ts` provides predefined transformation presets:

- `RESPONSIVE_BACKGROUND`: For large background images
- `CARD_THUMBNAIL`: For tour card images
- `LOGO`: For logo images
- `TEAM_PHOTO`: For team member photos

Example usage:

```vue
<cloudinary-image
  path="glacier-paradise/tours/snow-cat/topoftthediamond"
  alt="Snow Cat Tour"
  :options="CloudinaryPresets.CARD_THUMBNAIL"
/>
```

## Using the Native Cloudinary Components

The @nuxtjs/cloudinary module also provides native Cloudinary components:

```vue
<template>
  <CldImage
    src="iskyunum1_tinified"
    width="1200"
    height="600"
    crop="fill"
    gravity="auto"
  />
</template>
```

For more information on the native components, see the [Cloudinary Vue SDK documentation](https://cloudinary.com/documentation/vue_integration).

## Migrating Existing Images

When migrating existing images to Cloudinary, follow these steps:

1. Upload the image to Cloudinary using the folder structure defined in the migration plan
2. Update the image reference in the code to use the Cloudinary URL
3. Update the tracking document with the Cloudinary Asset ID

Example migration:

Before:

```vue
<img src="/images/topoftthediamond-resized.card.jpg" alt="Snow Cat Tour" />
```

After:

```vue
<cloudinary-image
  path="glacier-paradise/cards/topoftthediamond-resized"
  alt="Snow Cat Tour"
  :options="CloudinaryPresets.CARD_THUMBNAIL"
/>
```

## Background Images with Transitions

For the large background image on the landing page that has a transition effect, update the CSS in `scrolling-bg-image.vue`:

Before:

```scss
.scrolling-bg-image {
  background-image: url("/images/iskyunum1_tinified.avif");
  &.pngBgImage {
    background-image: url("/images/iskyunum1_tinified.png");
  }
}
```

After:

```vue
<script setup>
import { getCloudinaryUrl, CloudinaryPresets } from "~/utils/cloudinary";
const { isEdge } = useDevice();

const backgroundImageUrl = getCloudinaryUrl(
  "glacier-paradise/backgrounds/iskyunum1_tinified",
  CloudinaryPresets.RESPONSIVE_BACKGROUND
);
</script>

<style lang="scss" scoped>
.scrolling-bg-image {
  background-image: v-bind("`url(${backgroundImageUrl})`");
  /* No need for separate PNG fallback as Cloudinary handles format detection */
}
</style>
```
