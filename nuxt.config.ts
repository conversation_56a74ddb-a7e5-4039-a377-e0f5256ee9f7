const isDev = process.env.NODE_ENV === 'development'

export default defineNuxtConfig({
  compatibilityDate: "2025-05-02",
  nitro: {},
  css: [
    "@/assets/variables.scss",
    "@/assets/mixins.scss",
    "@/assets/base.scss",
  ],
  typescript: {
    strict: true,
  },
  build: {
    transpile: ["oh-vue-icons"],
  },
  vite: {
    optimizeDeps: {
      exclude: ["oh-vue-icons/icons"],
    },
    server: {
      hmr: {
        overlay: true
      },
      watch: {
        usePolling: false
      }
    }
  },
  components: [
    { path: "~/components/homeComponents", prefix: "Home" },
    { path: "~/components/aboutUsComponents", prefix: "About" },
    { path: "~/components/termsComponents", prefix: "Terms" },
    { path: "~/components/tourComponents", prefix: "Tour" },
    { path: "~/components/contactUsComponents", prefix: "ContactUs" },
    "~/components",
  ],
  app: {
    layoutTransition: { name: "layout", mode: "out-in" },
    head: {
      title: "Glacier Paradise, special sightseing tours of Snæfellsjökull",
      htmlAttrs: {
        lang: 'en',
      },
      meta: [
        {
          name: "description",
          content:
            "Discover the natural wonders of Snæfellsjökull with our exciting tours. Explore the stunning glacier and breathtaking landscapes of the peninsula. Book your adventure today and experience a unique glacier tour in Iceland",
        },
        {
          name: "google-site-verification",
          content: "5cm3phZ29SMyACRjaDPbQ1aK884k5I0HQIakV8Ple3E",
        },
        // Open Graph meta tags
        {
          property: "og:type",
          content: "website",
        },
        {
          property: "og:site_name",
          content: "Glacier Paradise",
        },
        {
          property: "og:locale",
          content: "en_US",
        },
        {
          property: "og:locale:alternate",
          content: "is_IS",
        },
        // Twitter Card meta tags
        {
          name: "twitter:card",
          content: "summary_large_image",
        },
        {
          name: "twitter:site",
          content: "@glacierparadise",
        },
        // Additional SEO meta tags
        {
          name: "robots",
          content: "index, follow",
        },
        {
          name: "author",
          content: "Glacier Paradise",
        },
        {
          name: "theme-color",
          content: "#1e40af",
        },
      ],
      link: [
        {
          rel: "icon",
          type: "image/x-icon",
          href: "/favicon.ico",
        },
        {
          rel: "canonical",
          href: "https://www.glacierparadise.is",
        },
      ],
      script: [
        {
          src: "https://beamanalytics.b-cdn.net/beam.min.js",
          "data-token": "e3a3aef5-4453-48c7-86a7-c65c14ef6554",
          async: true,
        },
      ],
    },
  },
  modules: [
    "@nuxtjs/i18n",
    "@nuxtjs/device",
    // Only load these in production or when explicitly needed
    ...(isDev && process.env.LOAD_ALL_MODULES !== 'true' ? [] : [
      "@nuxtjs/sitemap",
      "nuxt-site-config",
      "@nuxtjs/robots",
      "@nuxtjs/seo",
    ]),
    "nuxt-mail",
    "@nuxtjs/cloudinary",
  ],
  cloudinary: {
    cloudName: process.env.CLOUDINARY_CLOUD_NAME,
    apiKey: process.env.CLOUDINARY_API_KEY,
  },
  i18n: {
    lazy: true,
    langDir: "locales",
    strategy: "no_prefix",
    locales: [
      { name: "English", code: "en", language: "en-gb", file: "en.js" },
      { name: "Íslenska", code: "is", language: "is-IS", file: "is.js" },
    ],
    defaultLocale: "en",
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: "i18n_redirected",
      redirectOn: "root",
    },
  },
  runtimeConfig: {
    public: {
      siteUrl: "https://www.glacierparadise.is",
      siteName: "Glacier Paradise, special sightseing tours of Snæfellsjökull",
      siteDescription:
        "Discover the natural wonders of Snæfellsjökull with our exciting tours. Explore the stunning glacier and breathtaking landscapes of the peninsula. Book your adventure today and experience a unique glacier tour in Iceland",
      cloudinary: {
        cloudName: process.env.CLOUDINARY_CLOUD_NAME,
      },
      facebookAppId: process.env.FACEBOOK_APP_ID,
      facebookPageId: process.env.FACEBOOK_PAGE_ID
    },
    mail: {
      message: {
        to: process.env.NUXT_EMAIL_TO,
      },
      smtp: {
        host: process.env.NUXT_EMAIL_HOST,
        port: process.env.NUXT_EMAIL_PORT,
        auth: {
          user: process.env.NUXT_EMAIL_USERNAME,
          pass: process.env.NUXT_EMAIL_PASSWORD,
        },
      },
    },
  },
  devServer: {
    port: 3000,
    host: 'localhost',
    https: false
  },
});
